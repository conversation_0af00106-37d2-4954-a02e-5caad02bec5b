<script setup>
import { ref } from 'vue';
import ExchangeModule from './components/ExchangeModule.vue';
import HelloWorld from './components/HelloWorld.vue';
import TheWelcome from './components/TheWelcome.vue';
import data from './components/data.json';
import CouponList from './components/CouponList.vue';
import SapInvoices from './components/SapInvoices.vue';

// Reactive state
const showExchangeModal = ref(false);

// Methods
const handleExchangeSubmit = (formData) => {
  console.log('Exchange form submitted:', formData);
  // Process the form data here
};
</script>

<template>
    <CouponList />
    <SapInvoices/>
</template>

<style scoped>
header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>