@import "./fonts.css";
@import "tailwindcss";

@layer base {
  :root {
    --font-family-base: "Alexandria", sans-serif;
  }

  html {
    font-family: var(--font-family-base);
  }
}

@theme {
  --font-alexandria: "Alexandria", sans-serif;
}
.custom-scrollbar {
  scrollbar-width: thin; /* Firefox support */
  scrollbar-color: #ff0000 #ffffff; /* Scrollbar (thumb, track) */
  scrollbar-gutter: stable; /* Firefox support */

}
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #ffffff;

}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 50%; /* Changed to make the thumb fully round */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

/* Ensure the scrollbar plugin is included in your Tailwind configuration */
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
