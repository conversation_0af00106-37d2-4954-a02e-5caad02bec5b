<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-[5vw] font-medium"
  >
    <div
      class="bg-[#F6F6F6] p-2 rounded-lg w-full xs:max-w-[90vw] md:max-w-3xl max-h-[90vh] overflow-y-auto relative custom-scrollbar font-alexandria"
    >
      <!-- Modal Header -->
      <div class="p-5 bg-white mb-0 rounded-t-sm">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-bold xs:text-md md:text-xl">
            Exchange Product Details
          </h2>
          <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="bg-[#F6F6F6]">
        <form @submit.prevent="submitForm">
          <div class="w-full h-full bg-white p-5 rounded-b-sm">
            <!-- Brand Name -->
            <div class="mb-6">
              <label class="block text-sm font-medium mb-1">
                Brand Name <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.brandName"
                type="text"
                placeholder="Enter Exchange Product's Brand name"
                class="w-full px-3 py-2 my-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 placeholder:text-xs placeholder:font-semibold"
                required
                maxlength="20"
              />
              <div class="text-xs text-gray-500 mt-1">Max Char 20</div>
            </div>

            <!-- Product Specifications -->
            <div class="mb-6">
              <h3 class="text-sm font-medium mb-3">Product Specifications</h3>
              <div class="grid grid-cols-3 grid-rows-auto gap-4">
                <!-- Dynamic selection dropdowns -->
                <div v-for="(selection, index) in selectionPath" :key="index" class="grid-cols-1">
                  <label class="block text-sm font-medium mb-1">
                    {{ selection.label }} <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <select
                      v-model="selection.value"
                      class="w-full px-3 py-2 border text-sm border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                      @change="handleSelectionChange(index)"
                    >
                      <option value="">Select {{ selection.label }}</option>
                      <option
                        v-for="option in selection.options"
                        :key="option"
                        :value="option"
                      >
                        {{ option }}
                      </option>
                    </select>
                    <div
                      class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                    >
                      <svg
                        class="fill-current h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <!-- Current Selection Path -->
                
              </div>

              <!-- Year Selection -->
              <div class="mt-4">
                <label class="block text-sm font-medium mb-1">
                  Year of Manufacture / Purchase
                  <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <select
                    v-model="form.year"
                    class="w-full px-3 py-2 border text-sm border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-2 focus:ring-red-500"
                    required
                  >
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                    <option value="2020">2020</option>
                  </select>
                  <div
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                  >
                    <svg
                      class="fill-current h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Debug Button -->
             
            </div>

            <!-- Condition -->
            <div class="mb-6">
              <label class="block text-sm font-medium mb-1">
                Exchange Product's Condition <span class="text-red-500">*</span>
              </label>
              <div class="flex items-center gap-8 py-2">
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="form.condition"
                    value="working"
                    class="w-4 h-4 checked:border-gray-300 accent-red-500"
                    required
                  />
                  <span class="ml-2 text-sm">
                    Working ( Exchange Price:
                    {{ selectedPrices.working_price || 0 }} )
                  </span>
                </label>
                <label class="flex items-center">
                  <input
                    type="radio"
                    v-model="form.condition"
                    value="not-working"
                    class="h-4 w-4 checked:border-gray-300 accent-red-500"
                  />
                  <span class="ml-2 text-sm">
                    Not Working ( Exchange Price:
                    {{ selectedPrices.not_working_price || 0 }} )
                  </span>
                </label>
              </div>
            </div>

            <!-- Comment -->
            <div class="mb-6">
              <label class="block text-sm font-medium mb-1">
                Exchange Product's Comment
              </label>
              <textarea
                v-model="form.comment"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Enter your comments here..."
              ></textarea>
            </div>

            <div class="mt-3 bg-white rounded p-5 py-3">
              <h3 class="font-semibold mb-2">Exchange Product Details</h3>
              <ul class="text-xs text-gray-600 space-y-2 pl-2">
                <li class="flex">
                  <span class="mr-2">•</span>
                  <span class=""
                    >The exchange product will be checked during the time of
                    your product delivery</span
                  >
                </li>
                <li class="flex">
                  <span class="mr-2">•</span>
                  <span
                    >Please ensure the above furnished details are true. If not
                    an exchange product will be rejected and you may pay the
                    exchange discount amount at the time of delivery.</span
                  >
                </li>
                <li class="flex">
                  <span class="mr-2">•</span>
                  <span>Exchange is applicable only at Chennai region.</span>
                </li>
              </ul>
              <!-- Modal Footer -->
              <div class="p-5">
                <button
                  type="submit"
                  class="w-full py-2 bg-[#C41230] font-semibold text-sm text-white rounded-md transition duration-200"
                >
                  APPLY EXCHANGE
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ExchangeProductModal",
  props: {
    initialData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isOpen: true,
      form: {
        brandName: "",
        year: "2024",
        condition: "working",
        comment: ""
      },
      rootData: {},
      selectionPath: [],
      selectedPrices: {
        working_price: 0,
        not_working_price: 0
      }
    };
  },
  methods: {
    closeModal() {
      this.isOpen = false;
      this.$emit("close");
    },

    submitForm() {
      // Prepare form data with all selections
      const formData = { ...this.form };

      // Add all selections to the form data
      this.selectionPath.forEach(selection => {
        formData[selection.label] = selection.value;
      });

      console.log("Form submitted:", formData);
      this.$emit("submit", formData);
      this.closeModal();
    },

    // Initialize the first level of selection
    initializeSelections() {
      // Reset selections
      this.selectionPath = [];

      // Get top-level categories (like "electronics")
      const categories = Object.keys(this.rootData);

      if (categories.length > 0) {
        this.selectionPath.push({
          label: "Category",
          value: "",
          options: categories
        });
      }
    },

    // Fix for the handleSelectionChange method
    handleSelectionChange(index) {
      console.log(`Selection changed at index ${index}`);
      
      // Truncate the selection path to the current index
      this.selectionPath = this.selectionPath.slice(0, index + 1);
      
      // Reset prices initially
      this.selectedPrices = { working_price: 0, not_working_price: 0 };
      
      // If selection is empty, just return
      if (!this.selectionPath[index].value) return;
      
      // Get the current node based on selections
      const currentNode = this.navigateToCurrentNode();
      console.log("Current node:", currentNode);
      if (!currentNode) return;
      
      // If we're at a leaf node (has prices), update the prices
      if (currentNode.subgroup === false) {
        this.selectedPrices.working_price = currentNode.working_price || 0;
        this.selectedPrices.not_working_price = currentNode.not_working_price || 0;
        return;
      }
      
      // If we're at a parent node with children, add the next dropdown
      if (currentNode.subgroup === true && currentNode.children) {
        const nextLevel = this.getNextSelectionLevel(currentNode);
        if (nextLevel) {
          this.selectionPath.push(nextLevel);
        }
      }
    },

    // Completely dynamic navigation function
    navigateToCurrentNode() {
      if (this.selectionPath.length === 0 || !this.selectionPath[0].value) {
        return null;
      }
      
      // Start at root
      let currentNode = this.rootData;
      
      // Navigate through each selection level
      for (let i = 0; i < this.selectionPath.length; i++) {
        const selection = this.selectionPath[i];
        if (!selection.value) break;
        
        // For first level, select directly from root
        if (i === 0) {
          currentNode = currentNode[selection.value];
        } 
        // For subsequent levels, look in children
        else if (currentNode.children && currentNode.children[selection.label]) {
          currentNode = currentNode.children[selection.label][selection.value];
        }
        // If structure is different, try direct access
        else if (currentNode.children) {
          currentNode = currentNode.children[selection.value];
        }
        
        if (!currentNode) return null;
      }
      
      return currentNode;
    },

    // Dynamic next level detection
    getNextSelectionLevel(node) {
      if (!node.children) return null;
      
      // Get the first level of children keys
      const childKeys = Object.keys(node.children);
      if (childKeys.length === 0) return null;
      
      // Use the first child key as the label for the next dropdown
      const nextLabel = childKeys[0];
      const options = Object.keys(node.children[nextLabel]);
      
      return {
        label: nextLabel,
        value: "",
        options: options
      };
    },

    // Update prices based on current selections
    updatePrices() {
      const node = this.navigateToCurrentNode();

      if (node && !node.subgroup) {
        this.selectedPrices = {
          working_price: node.working_price || 0,
          not_working_price: node.not_working_price || 0
        };
      } else {
        this.selectedPrices = { working_price: 0, not_working_price: 0 };
      }
    },

    // Debug function
    logState() {
      console.log('Current selections:', this.selectionPath.map(s => `${s.label}: ${s.value}`));
      console.log('Current node:', this.navigateToCurrentNode());
      console.log('Current prices:', this.selectedPrices);
    }
  },
  created() {
    // Set root data from props
    this.rootData = this.initialData;

    // Initialize selections
    this.$nextTick(() => {
      this.initializeSelections();
    });
  }
};
</script>
